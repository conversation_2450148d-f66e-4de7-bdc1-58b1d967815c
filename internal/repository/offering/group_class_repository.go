package offering

import (
	"context"
	"github.com/MoeGolibrary/go-lib/grpc"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
)

//go:generate mockgen -package=offering -destination=mocks/mock_group_class_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering GroupClassRepository
type GroupClassRepository interface {
	ListInstances(ctx context.Context, instanceIDs []int64) ([]*offeringpb.GroupClassInstance, error)
	GetInstance(ctx context.Context, instanceID int64) (*offeringpb.GroupClassInstance, error)
	GetSession(ctx context.Context, sessionID int64) (*offeringpb.GroupClassSession, error)
	ListSessions(ctx context.Context, filter filter.ListSessionsFilter) ([]*offeringpb.GroupClassSession, error)
}

type groupClassRepository struct {
	client offeringsvcpb.GroupClassServiceClient
}

func (r groupClassRepository) GetSession(ctx context.Context, sessionID int64) (*offeringpb.GroupClassSession, error) {
	request := &offeringsvcpb.ListSessionsRequest{
		SessionIds: []int64{sessionID},
	}
	response, err := r.client.ListSessions(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.GetSessions()[0], nil
}

func (r groupClassRepository) ListInstances(ctx context.Context, instanceIDs []int64) ([]*offeringpb.GroupClassInstance, error) {
	if len(instanceIDs) == 0 {
		return nil, nil
	}
	request := &offeringsvcpb.ListInstancesRequest{
		Ids: instanceIDs,
	}
	response, err := r.client.ListInstances(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.GetGroupClassInstances(), nil
}

func (r groupClassRepository) GetInstance(ctx context.Context, instanceID int64) (*offeringpb.GroupClassInstance, error) {
	request := &offeringsvcpb.GetInstanceRequest{
		Id: instanceID,
	}
	response, err := r.client.GetInstance(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.GetGroupClassInstance(), nil
}

func (r groupClassRepository) ListSessions(ctx context.Context, filter filter.ListSessionsFilter) ([]*offeringpb.GroupClassSession, error) {
	if len(filter.InstanceIDs) == 0 && len(filter.SessionIDs) == 0 {
		return nil, nil
	}
	request := &offeringsvcpb.ListSessionsRequest{
		GroupClassInstanceId: filter.InstanceIDs,
		SessionIds:           filter.SessionIDs,
	}
	response, err := r.client.ListSessions(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.GetSessions(), nil
}

func NewGroupClassRepository() GroupClassRepository {
	return &groupClassRepository{
		client: grpc.NewClient(consts.OfferingEndpoint, offeringsvcpb.NewGroupClassServiceClient),
	}
}
