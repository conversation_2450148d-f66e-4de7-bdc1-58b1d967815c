// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering (interfaces: GroupClassRepository)
//
// Generated by this command:
//
//	mockgen -package=offering -destination=mocks/mock_group_class_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering GroupClassRepository
//

// Package offering is a generated GoMock package.
package offering

import (
	context "context"
	offering "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	reflect "reflect"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockGroupClassRepository is a mock of GroupClassRepository interface.
type MockGroupClassRepository struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClassRepositoryMockRecorder
	isgomock struct{}
}

// MockGroupClassRepositoryMockRecorder is the mock recorder for MockGroupClassRepository.
type MockGroupClassRepositoryMockRecorder struct {
	mock *MockGroupClassRepository
}

// NewMockGroupClassRepository creates a new mock instance.
func NewMockGroupClassRepository(ctrl *gomock.Controller) *MockGroupClassRepository {
	mock := &MockGroupClassRepository{ctrl: ctrl}
	mock.recorder = &MockGroupClassRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClassRepository) EXPECT() *MockGroupClassRepositoryMockRecorder {
	return m.recorder
}

// GetInstance mocks base method.
func (m *MockGroupClassRepository) GetInstance(ctx context.Context, instanceID int64) (*offeringpb.GroupClassInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstance", ctx, instanceID)
	ret0, _ := ret[0].(*offeringpb.GroupClassInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstance indicates an expected call of GetInstance.
func (mr *MockGroupClassRepositoryMockRecorder) GetInstance(ctx, instanceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstance", reflect.TypeOf((*MockGroupClassRepository)(nil).GetInstance), ctx, instanceID)
}

// GetSession mocks base method.
func (m *MockGroupClassRepository) GetSession(ctx context.Context, sessionID int64) (*offeringpb.GroupClassSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSession", ctx, sessionID)
	ret0, _ := ret[0].(*offeringpb.GroupClassSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSession indicates an expected call of GetSession.
func (mr *MockGroupClassRepositoryMockRecorder) GetSession(ctx, sessionID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSession", reflect.TypeOf((*MockGroupClassRepository)(nil).GetSession), ctx, sessionID)
}

// ListInstances mocks base method.
func (m *MockGroupClassRepository) ListInstances(ctx context.Context, instanceIDs []int64) ([]*offeringpb.GroupClassInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListInstances", ctx, instanceIDs)
	ret0, _ := ret[0].([]*offeringpb.GroupClassInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListInstances indicates an expected call of ListInstances.
func (mr *MockGroupClassRepositoryMockRecorder) ListInstances(ctx, instanceIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListInstances", reflect.TypeOf((*MockGroupClassRepository)(nil).ListInstances), ctx, instanceIDs)
}

// ListSessions mocks base method.
func (m *MockGroupClassRepository) ListSessions(ctx context.Context, filter offering.ListSessionsFilter) ([]*offeringpb.GroupClassSession, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSessions", ctx, filter)
	ret0, _ := ret[0].([]*offeringpb.GroupClassSession)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSessions indicates an expected call of ListSessions.
func (mr *MockGroupClassRepositoryMockRecorder) ListSessions(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSessions", reflect.TypeOf((*MockGroupClassRepository)(nil).ListSessions), ctx, filter)
}
