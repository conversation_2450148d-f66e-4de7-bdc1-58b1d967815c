// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment (interfaces: GroupClassDetailRepository)
//
// Generated by this command:
//
//	mockgen -package=fulfillment -destination=mocks/mock_group_class_detail_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment GroupClassDetailRepository
//

// Package fulfillment is a generated GoMock package.
package fulfillment

import (
	context "context"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	reflect "reflect"

	fulfillment "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	query "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	gomock "go.uber.org/mock/gomock"
)

// MockGroupClassDetailRepository is a mock of GroupClassDetailRepository interface.
type MockGroupClassDetailRepository struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClassDetailRepositoryMockRecorder
	isgomock struct{}
}

// MockGroupClassDetailRepositoryMockRecorder is the mock recorder for MockGroupClassDetailRepository.
type MockGroupClassDetailRepositoryMockRecorder struct {
	mock *MockGroupClassDetailRepository
}

// NewMockGroupClassDetailRepository creates a new mock instance.
func NewMockGroupClassDetailRepository(ctrl *gomock.Controller) *MockGroupClassDetailRepository {
	mock := &MockGroupClassDetailRepository{ctrl: ctrl}
	mock.recorder = &MockGroupClassDetailRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClassDetailRepository) EXPECT() *MockGroupClassDetailRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockGroupClassDetailRepository) Create(ctx context.Context, model *model.GroupClassDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, model)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockGroupClassDetailRepositoryMockRecorder) Create(ctx, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).Create), ctx, model)
}

// GetByID mocks base method.
func (m *MockGroupClassDetailRepository) GetByID(ctx context.Context, id int64) (*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockGroupClassDetailRepositoryMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).GetByID), ctx, id)
}

// ListByFilter mocks base method.
func (m *MockGroupClassDetailRepository) ListByFilter(ctx context.Context, filter filter.ListGroupClassDetailFilter) ([]*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByFilter", ctx, filter)
	ret0, _ := ret[0].([]*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByFilter indicates an expected call of ListByFilter.
func (mr *MockGroupClassDetailRepositoryMockRecorder) ListByFilter(ctx, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByFilter", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).ListByFilter), ctx, filter)
}

// Update mocks base method.
func (m *MockGroupClassDetailRepository) Update(ctx context.Context, model *model.GroupClassDetail) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, model)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockGroupClassDetailRepositoryMockRecorder) Update(ctx, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).Update), ctx, model)
}

// WithQuery mocks base method.
func (m *MockGroupClassDetailRepository) WithQuery(q *query.Query) fulfillment.GroupClassDetailRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithQuery", q)
	ret0, _ := ret[0].(fulfillment.GroupClassDetailRepository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockGroupClassDetailRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithQuery", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).WithQuery), q)
}
