package fulfillment

import (
	"context"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
)

var Converter = &impl.FulfillmentConverter{}

//go:generate mockgen -package=fulfillment -destination=mocks/mock_fulfillment_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	Create(ctx context.Context, model *model.Fulfillment) error
	Update(ctx context.Context, model *model.Fulfillment) (int64, error)
	GetByID(ctx context.Context, id int64) (*model.Fulfillment, error)
	List(ctx context.Context, filter *filter.ListFulfillmentFilter, pagination *utilsV2.PaginationRequest) ([]*model.Fulfillment, error)
	UpdateSelective(ctx context.Context, update dto.UpdateFulfillmentDTO) error
}

type repository struct {
	q *query.Query
}

func (r repository) UpdateSelective(ctx context.Context, update dto.UpdateFulfillmentDTO) error {
	updateOpt := Converter.UpdateToOpt(update)
	_, err := r.q.WithContext(ctx).Fulfillment.
		Where(r.q.Fulfillment.ID.Eq(update.FulfillmentID)).
		Updates(gormx.Update(updateOpt))
	if err != nil {
		return err
	}
	return nil
}

func (r repository) WithQuery(q *query.Query) Repository {
	if q != nil {
		return &repository{q: q}
	}
	return r
}

func (r repository) Create(ctx context.Context, model *model.Fulfillment) error {
	return r.q.WithContext(ctx).Fulfillment.Create(model)
}

func (r repository) Update(ctx context.Context, model *model.Fulfillment) (int64, error) {
	result, err := r.q.WithContext(ctx).Fulfillment.Where(r.q.Fulfillment.ID.Eq(model.ID)).Updates(model)
	if err != nil {
		return 0, err
	}
	return result.RowsAffected, nil
}

func (r repository) GetByID(ctx context.Context, id int64) (*model.Fulfillment, error) {
	return r.q.WithContext(ctx).Fulfillment.Where(r.q.Fulfillment.ID.Eq(id)).First()
}

func (r repository) List(ctx context.Context, filter *filter.ListFulfillmentFilter, pagination *utilsV2.PaginationRequest) ([]*model.Fulfillment, error) {
	if filter == nil {
		return []*model.Fulfillment{}, nil
	}
	selectQuery := r.q.WithContext(ctx).Fulfillment.Select()
	if len(filter.IDs) > 0 {
		selectQuery.Where(r.q.Fulfillment.ID.In(filter.IDs...))
	}
	if filter.CompanyID != nil && *filter.CompanyID != 0 {
		selectQuery.Where(r.q.Fulfillment.CompanyID.Eq(*filter.CompanyID))
	}
	if len(filter.BusinessIDs) > 0 {
		selectQuery.Where(r.q.Fulfillment.BusinessID.In(filter.BusinessIDs...))
	}
	if len(filter.Statuses) > 0 {
		selectQuery.Where(r.q.Fulfillment.Status.In(util.EnumSliceValuer(filter.Statuses)...))
	}
	if len(filter.CustomerIDs) > 0 {
		selectQuery.Where(r.q.Fulfillment.CustomerID.In(filter.CustomerIDs...))
	}
	if filter.CreatedAtMin != nil {
		selectQuery.Where(r.q.Fulfillment.CreatedAt.Gte(*filter.CreatedAtMin))
	}
	if pagination != nil {
		selectQuery.Limit(int(pagination.GetPageSize())).Offset(int(pagination.GetPageNum()-1) * int(pagination.GetPageSize()))
	}
	result, err := selectQuery.Find()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func NewFulfillmentRepository(db *gorm.DB) Repository {
	return &repository{
		q: query.Use(db),
	}
}
