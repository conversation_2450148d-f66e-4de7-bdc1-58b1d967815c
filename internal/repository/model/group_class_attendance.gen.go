// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameGroupClassAttendance = "group_class_attendance"

// GroupClassAttendance mapped from table <group_class_attendance>
type GroupClassAttendance struct {
	ID                  int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CompanyID           int64          `gorm:"column:company_id;type:bigint;not null" json:"company_id"`
	BusinessID          int64          `gorm:"column:business_id;type:bigint;not null" json:"business_id"`
	FulfillmentID       int64          `gorm:"column:fulfillment_id;type:bigint;not null;index:group_class_attendance_idx_fulfillment,priority:1;comment:fulfillment.id" json:"fulfillment_id"` // fulfillment.id
	GroupClassDetailID  int64          `gorm:"column:group_class_detail_id;type:bigint;not null;comment:group_class_detail.id" json:"group_class_detail_id"`                                    // group_class_detail.id
	GroupClassSessionID int64          `gorm:"column:group_class_session_id;type:bigint;not null;comment:training_session.id" json:"group_class_session_id"`                                    // training_session.id
	PetID               int64          `gorm:"column:pet_id;type:bigint;not null" json:"pet_id"`
	CheckInTime         time.Time      `gorm:"column:check_in_time;type:timestamp without time zone;not null;comment:Check in time of pet" json:"check_in_time"` // Check in time of pet
	CreatedAt           *time.Time     `gorm:"column:created_at;type:timestamp without time zone;not null;default:now()" json:"created_at"`
	UpdatedAt           *time.Time     `gorm:"column:updated_at;type:timestamp without time zone;not null;default:now()" json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
}

// TableName GroupClassAttendance's table name
func (*GroupClassAttendance) TableName() string {
	return TableNameGroupClassAttendance
}
