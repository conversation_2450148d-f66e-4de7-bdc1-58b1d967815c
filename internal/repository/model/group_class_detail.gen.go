// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	"time"

	"gorm.io/gorm"
)

const TableNameGroupClassDetail = "group_class_detail"

// GroupClassDetail mapped from table <group_class_detail>
type GroupClassDetail struct {
	ID                   int64                                      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	FulfillmentID        int64                                      `gorm:"column:fulfillment_id;type:bigint;not null;index:group_class_detail_idx_fulfillment,priority:1;comment:fulfillment.id" json:"fulfillment_id"` // fulfillment.id
	PetID                int64                                      `gorm:"column:pet_id;type:bigint;not null;comment:moe_customer_pet.id" json:"pet_id"`                                                                // moe_customer_pet.id
	GroupClassID         int64                                      `gorm:"column:group_class_id;type:bigint;not null;comment:group class id, service.id" json:"group_class_id"`                                         // group class id, service.id
	GroupClassInstanceID int64                                      `gorm:"column:group_class_instance_id;type:bigint;not null;comment:group_class_instance.id" json:"group_class_instance_id"`                          // group_class_instance.id
	Status               fulfillmentpb.GroupClassDetailModel_Status `gorm:"column:status;type:integer;not null;comment:1-Not started, 2-In progress, 3-Completed" json:"status"`                                         // 1-Not started, 2-In progress, 3-Completed
	CreatedAt            *time.Time                                 `gorm:"column:created_at;type:timestamp without time zone;not null;default:now()" json:"created_at"`
	UpdatedAt            *time.Time                                 `gorm:"column:updated_at;type:timestamp without time zone;not null;default:now()" json:"updated_at"`
	DeletedAt            gorm.DeletedAt                             `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
}

// TableName GroupClassDetail's table name
func (*GroupClassDetail) TableName() string {
	return TableNameGroupClassDetail
}
